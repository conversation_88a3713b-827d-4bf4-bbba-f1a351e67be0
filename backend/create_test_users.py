#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
django.setup()

from users.models import User

def create_test_users():
    # Create test users
    users_data = [
        {
            'email': '<EMAIL>',
            'username': 'alice',
            'password': 'password123',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>'
        },
        {
            'email': '<EMAIL>',
            'username': 'bob',
            'password': 'password123',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>'
        },
        {
            'email': '<EMAIL>',
            'username': 'charlie',
            'password': 'password123',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>'
        }
    ]

    for user_data in users_data:
        user, created = User.objects.get_or_create(
            email=user_data['email'],
            defaults={
                'username': user_data['username'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name']
            }
        )
        if created:
            user.set_password(user_data['password'])
            user.save()
            print(f"Created user: {user.email}")
        else:
            print(f"User already exists: {user.email}")

if __name__ == '__main__':
    create_test_users()
    print("Test users created successfully!")
