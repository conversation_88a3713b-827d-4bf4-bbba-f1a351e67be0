# backend/messaging/schemas.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum
import uuid

class ConversationType(str, Enum):
    DIRECT = "DIRECT"
    GROUP = "GROUP"

class MessageType(str, Enum):
    TEXT = "TEXT"
    IMAGE = "IMAGE"
    FILE = "FILE"
    SYSTEM = "SYSTEM"

class ParticipantRole(str, Enum):
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"

class UserBasic(BaseModel):
    id: uuid.UUID
    username: str
    first_name: str
    last_name: str
    profile_picture: Optional[str] = None

    class Config:
        from_attributes = True

class MessageCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=4000)
    message_type: MessageType = MessageType.TEXT
    reply_to_id: Optional[uuid.UUID] = None

class MessageResponse(BaseModel):
    id: uuid.UUID
    conversation_id: uuid.UUID
    sender: UserBasic
    content: str
    message_type: MessageType
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    type: ConversationType
    name: Optional[str] = Field(None, max_length=100)
    participant_ids: List[uuid.UUID] = Field(..., min_items=1)

    @validator('name')
    def validate_name_for_group(cls, v, values):
        if values.get('type') == ConversationType.GROUP and not v:
            raise ValueError('Group conversations must have a name')
        return v

class ConversationResponse(BaseModel):
    id: uuid.UUID
    type: ConversationType
    name: Optional[str]
    participants: List[UserBasic]
    last_message: Optional[MessageResponse]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
