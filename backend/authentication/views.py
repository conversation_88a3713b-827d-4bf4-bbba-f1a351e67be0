# backend/authentication/views.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from pydantic import ValidationError
from users.models import User
from users.schemas import UserCreate, UserLogin, UserResponse, AuthResponse, TokenResponse

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    try:
        # Validate input with Pydantic
        user_data = UserCreate(**request.data)

        # Check if user already exists
        if User.objects.filter(email=user_data.email).exists():
            return Response({
                'success': False,
                'error': 'User with this email already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        if User.objects.filter(username=user_data.username).exists():
            return Response({
                'success': False,
                'error': 'User with this username already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create user
        user = User.objects.create(
            email=user_data.email,
            username=user_data.username,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            password=make_password(user_data.password)
        )

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Prepare response using Pydantic schemas
        user_response = UserResponse.from_orm(user)
        tokens = TokenResponse(
            access=str(refresh.access_token),
            refresh=str(refresh)
        )
        auth_response = AuthResponse(user=user_response, tokens=tokens)

        return Response({
            'success': True,
            'data': auth_response.dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    try:
        # Validate input with Pydantic
        login_data = UserLogin(**request.data)

        # Authenticate user
        user = authenticate(email=login_data.email, password=login_data.password)

        if user:
            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Prepare response using Pydantic schemas
            user_response = UserResponse.from_orm(user)
            tokens = TokenResponse(
                access=str(refresh.access_token),
                refresh=str(refresh)
            )
            auth_response = AuthResponse(user=user_response, tokens=tokens)

            return Response({
                'success': True,
                'data': auth_response.dict()
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'error': 'Invalid credentials'
        }, status=status.HTTP_401_UNAUTHORIZED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def profile(request):
    try:
        # Return current user profile
        user_response = UserResponse.from_orm(request.user)
        return Response({
            'success': True,
            'data': user_response.dict()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
