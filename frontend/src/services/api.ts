// frontend/src/services/api.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import type { ApiResponse, BaseQueryError } from '../types';

// Custom base query with authentication and error handling
const baseQueryWithAuth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: (headers, { endpoint }) => {
      // Don't add authorization headers for login and register endpoints
      const publicEndpoints = ['login', 'register'];
      const isPublicEndpoint = publicEndpoints.includes(endpoint);

      const token = localStorage.getItem('token');
      if (token && !isPublicEndpoint) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  // Handle 401 errors (token expired)
  if (result.error && result.error.status === 401) {
    // Since we don't have a refresh endpoint, clear tokens and redirect to login
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');

    // Only redirect if we're not already on the login page
    if (window.location.pathname !== '/login' && window.location.pathname !== '/register') {
      window.location.href = '/login';
    }
  }

  return result;
};

// Main API slice
export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithAuth,
  tagTypes: [
    'User',
    'Conversation', 
    'Message',
    'Auth'
  ],
  endpoints: () => ({}),
});

// Export hooks for usage in functional components
export const {
  // Base API hooks - these will be populated by injected endpoints
} = api;
