// frontend/src/hooks/useSocketCacheSync.ts
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSocket } from '../contexts/SocketContext';
import {
  addMessageToCache,
  updateConversationInCache,
  updateConversationLastMessage,
  api
} from '../services';
import type { AppDispatch } from '../store';
import type { Message } from '../services/messageApi';
import type { Conversation } from '../services/conversationApi';

/**
 * Hook to synchronize socket events with RTK Query cache
 * This ensures real-time updates are reflected in the cached data
 */
export const useSocketCacheSync = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { socket } = useSocket();

  useEffect(() => {
    if (!socket) return;

    // Handle new messages from socket
    const handleNewMessage = (data: { message: Message }) => {
      const { message } = data;
      
      // Add message to cache
      dispatch(addMessageToCache(message.conversationId, message));
      
      // Update conversation's last message
      dispatch(updateConversationLastMessage(message.conversationId, {
        id: message.id,
        content: message.content,
        sender: {
          username: message.sender.username
        },
        createdAt: message.createdAt
      }));
    };

    // Handle conversation updates from socket
    const handleConversationUpdate = (data: { conversation: Conversation }) => {
      const { conversation } = data;
      dispatch(updateConversationInCache(conversation));
    };

    // Handle new conversation creation from socket
    const handleNewConversation = (data: { conversation: Conversation }) => {
      const { conversation } = data;
      dispatch(updateConversationInCache(conversation));
    };

    // Handle message status updates (read receipts, etc.)
    const handleMessageUpdate = (data: { 
      conversationId: string; 
      messageId: string; 
      updates: Partial<Message> 
    }) => {
      const { conversationId, messageId, updates } = data;
      
      // Update specific message in cache
      dispatch(
        api.util.updateQueryData('getMessages', { conversationId }, (draft) => {
          if (draft.data && draft.data.results) {
            const message = draft.data.results.find(msg => msg.id === messageId);
            if (message) {
              Object.assign(message, updates);
            }
          }
        })
      );
    };

    // Handle typing indicators
    const handleTypingStart = (data: { 
      conversationId: string; 
      userId: string; 
      username: string 
    }) => {
      // This could be handled by a separate typing state if needed
      console.log('User started typing:', data);
    };

    const handleTypingStop = (data: { 
      conversationId: string; 
      userId: string 
    }) => {
      // This could be handled by a separate typing state if needed
      console.log('User stopped typing:', data);
    };

    // Handle user status updates
    const handleUserStatusUpdate = (data: { 
      userId: string; 
      status: 'online' | 'offline'; 
      lastSeen?: string 
    }) => {
      // Update user status in relevant caches
      console.log('User status updated:', data);
    };

    // Register socket event listeners
    socket.on('new_message', handleNewMessage);
    socket.on('conversation_updated', handleConversationUpdate);
    socket.on('new_conversation', handleNewConversation);
    socket.on('message_updated', handleMessageUpdate);
    socket.on('typing_start', handleTypingStart);
    socket.on('typing_stop', handleTypingStop);
    socket.on('user_status_update', handleUserStatusUpdate);

    // Cleanup listeners on unmount
    return () => {
      socket.off('new_message', handleNewMessage);
      socket.off('conversation_updated', handleConversationUpdate);
      socket.off('new_conversation', handleNewConversation);
      socket.off('message_updated', handleMessageUpdate);
      socket.off('typing_start', handleTypingStart);
      socket.off('typing_stop', handleTypingStop);
      socket.off('user_status_update', handleUserStatusUpdate);
    };
  }, [socket, dispatch]);
};
