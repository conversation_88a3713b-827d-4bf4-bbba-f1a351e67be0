// frontend/src/components/Chat/ChatRoom.tsx
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSocket } from '../../contexts/SocketContext';
import { fetchMessages } from '../../store/slices/messageSlice';
import type { AppDispatch } from '../../store';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { useAuth } from '../../contexts/AuthContext';

interface ChatRoomProps {
  conversationId: string;
}

const ChatRoom: React.FC<ChatRoomProps> = ({ conversationId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { joinConversation, isConnected } = useSocket();
  const { user } = useAuth();

  // Messages are handled by MessageList component

  useEffect(() => {
    if (conversationId) {
      // Fetch message history from API
      dispatch(fetchMessages({ conversationId }));

      // Join the conversation room for real-time updates
      if (isConnected) {
        joinConversation(conversationId);
      }
    }
  }, [conversationId, dispatch, joinConversation, isConnected]);

  if (!user) {
    return <div>Please log in to access chat</div>;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Connection status */}
      {!isConnected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="flex items-center space-x-2 text-yellow-800 text-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span>Reconnecting...</span>
          </div>
        </div>
      )}

      {/* Messages */}
      <MessageList
        conversationId={conversationId}
        currentUserId={user.id}
      />

      {/* Message input */}
      <MessageInput
        conversationId={conversationId}
        disabled={!isConnected}
      />
    </div>
  );
};

export default ChatRoom;
