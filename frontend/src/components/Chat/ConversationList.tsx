// frontend/src/components/Chat/ConversationList.tsx
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import type { RootState, AppDispatch } from '../../store';
import { selectConversation } from '../../store/slices/conversationSlice';
import { useGetConversationsQuery } from '../../services/conversationApi';
import type { Conversation, DraftConversation } from '../../store/slices/conversationSlice';

interface ConversationListProps {
  currentUserId: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  currentUserId
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { data: conversationsData, isLoading, error } = useGetConversationsQuery();

  const {
    draftConversations,
    selectedConversationId
  } = useSelector((state: RootState) => state.conversations);

  const conversations = conversationsData?.results || [];

  const handleSelectConversation = (conversationId: string) => {
    dispatch(selectConversation(conversationId));
  };

  // Combine real conversations and draft conversations
  const allConversations = [
    ...draftConversations.map(draft => ({
      ...draft,
      lastMessage: undefined // Draft conversations don't have messages yet
    })),
    ...conversations
  ];

  const getConversationName = (conversation: Conversation | (DraftConversation & { lastMessage?: any })) => {
    if (conversation.type === 'GROUP') {
      return conversation.name || 'Group Chat';
    }

    // For direct messages, show the other participant's name
    const otherParticipant = conversation.participants.find(
      p => p.id !== currentUserId
    );
    return otherParticipant
      ? `${otherParticipant.first_name || ''} ${otherParticipant.last_name || ''}`.trim() || otherParticipant.username || 'Unknown User'
      : 'Unknown User';
  };

  const getConversationAvatar = (conversation: Conversation | (DraftConversation & { lastMessage?: any })) => {
    if (conversation.type === 'GROUP') {
      return (
        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
          <Icon name="users" size={20} />
        </div>
      );
    }

    const otherParticipant = conversation.participants.find(
      p => p.id !== currentUserId
    );

    if (otherParticipant?.profile_picture) {
      return (
        <img
          src={otherParticipant.profile_picture}
          alt={`${otherParticipant.first_name} ${otherParticipant.last_name}`}
          className="w-12 h-12 rounded-full object-cover"
        />
      );
    }

    const initials = otherParticipant
      ? `${otherParticipant.first_name?.[0] || ''}${otherParticipant.last_name?.[0] || ''}`.toUpperCase() || '?'
      : '?';

    return (
      <div className="w-12 h-12 rounded-full bg-gray-400 flex items-center justify-center text-white font-medium">
        {initials}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex items-center space-x-2 text-gray-500">
          <Icon name="loader" size={20} className="animate-spin" />
          <span>Loading conversations...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center text-red-500">
          <Icon name="alert-triangle" size={48} className="mx-auto mb-2" />
          <p className="font-medium">Error loading conversations</p>
          <p className="text-sm">{error}</p>
          <button
            onClick={() => dispatch(fetchConversations())}
            className="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      {allConversations.map((conversation) => {
        const isSelected = conversation.id === selectedConversationId;

        return (
          <button
            key={conversation.id}
            onClick={() => handleSelectConversation(conversation.id)}
            className={`w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 ${
              isSelected ? 'bg-blue-50 border-blue-200' : ''
            }`}
          >
            <div className="flex items-center space-x-3">
              {getConversationAvatar(conversation)}

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {getConversationName(conversation)}
                  </h3>
                  <span className="text-xs text-gray-500">
                    {conversation.updatedAt 
                      ? formatDistanceToNow(new Date(conversation.updatedAt), { addSuffix: true })
                      : 'No date'}
                  </span>
                </div>

                {conversation.lastMessage ? (
                  <p className="text-sm text-gray-600 truncate mt-1">
                    <span className="font-medium">
                      {conversation.lastMessage.sender.username}:
                    </span>{' '}
                    {conversation.lastMessage.content}
                  </p>
                ) : 'isDraft' in conversation ? (
                  <p className="text-sm text-gray-500 italic mt-1">
                    Start a conversation...
                  </p>
                ) : null}
              </div>
            </div>
          </button>
        );
      })}

      {conversations.length === 0 && !isLoading && (
        <div className="p-8 text-center text-gray-500">
          <Icon name="message-circle" size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No conversations yet</p>
          <p className="text-sm">Start a new conversation to get started</p>
        </div>
      )}
    </div>
  );
};

export default ConversationList;
