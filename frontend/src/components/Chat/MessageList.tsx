// frontend/src/components/Chat/MessageList.tsx
import React, { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import type { RootState } from '../../store';
import type { Message } from '../../store/slices/messageSlice';

interface MessageListProps {
  conversationId: string;
  currentUserId: string;
  onReply?: (message: Message) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  conversationId,
  currentUserId,
  onReply
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const messages = useSelector((state: RootState) =>
    state.messages.messages[conversationId] || []
  );
  const sendingMessages = useSelector((state: RootState) =>
    state.messages.sendingMessages
  );
  const typingUsers = useSelector((state: RootState) =>
    state.messages.typingUsers[conversationId] || []
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getInitials = (first_name?: string, last_name?: string) => {
    if (!first_name && !last_name) return '?';
    return `${first_name?.[0] || ''}${last_name?.[0] || ''}`.toUpperCase() || '?';
  };

  const isMessageSending = (messageId: string) => {
    return sendingMessages[messageId] || false;
  };

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4">
      {messages.map((message) => {
        const isOwnMessage = message.sender.id === currentUserId;
        const isSending = isMessageSending(message.id);

        return (
          <div
            key={message.id}
            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex max-w-[70%] items-end space-x-2 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {!isOwnMessage && (
                <div className="flex-shrink-0">
                  {message.sender.profile_picture ? (
                    <img
                      src={message.sender.profile_picture}
                      alt={`${message.sender.first_name} ${message.sender.last_name}`}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white text-sm font-medium">
                      {getInitials(message.sender.first_name, message.sender.last_name)}
                    </div>
                  )}
                </div>
              )}

              <div
                className={`relative group rounded-lg px-4 py-2 max-w-full ${
                  isOwnMessage
                    ? 'bg-blue-600 text-white rounded-br-none'
                    : 'bg-gray-100 text-gray-900 rounded-bl-none'
                } ${isSending ? 'opacity-70' : ''}`}
              >
                <div className="text-sm leading-relaxed">
                  {message.content}
                </div>

                <div className={`flex items-center justify-between mt-1 ${
                  isOwnMessage ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  <div className="text-xs opacity-70">
                    {message.createdAt 
                      ? formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })
                      : 'No date'}
                  </div>

                  {isSending && (
                    <div className="flex items-center space-x-1 text-xs">
                      <Icon name="loader" size={12} className="animate-spin" />
                      <span>Sending...</span>
                    </div>
                  )}
                </div>

                {/* Message actions */}
                {onReply && !isSending && (
                  <button
                    onClick={() => onReply(message)}
                    className={`absolute top-0 right-0 transform translate-x-8 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full ${
                      isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                    } hover:bg-opacity-80`}
                    title="Reply to message"
                  >
                    <Icon name="message-circle" size={14} />
                  </button>
                )}
              </div>
            </div>
          </div>
        );
      })}

      {/* Typing indicators */}
      {typingUsers.length > 0 && (
        <div className="flex justify-start">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-gray-600 text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>
                {typingUsers.length === 1 ? 'Someone is' : `${typingUsers.length} people are`} typing...
              </span>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
