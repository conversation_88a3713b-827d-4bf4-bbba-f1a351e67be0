// frontend/src/components/ui/Icon.tsx
import React from 'react';
import {
  User,
  Users,
  MessageCircle,
  Send,
  Settings,
  LogOut,
  Eye,
  EyeOff,
  Search,
  Plus,
  X,
  Check,
  AlertCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react';

// Icon component mapping for consistent usage
export const Icons = {
  user: User,
  users: Users,
  message: MessageCircle,
  'message-circle': MessageCircle,
  send: Send,
  settings: Settings,
  logout: LogOut,
  'log-out': LogOut,
  eye: Eye,
  eyeOff: EyeOff,
  search: Search,
  plus: Plus,
  close: X,
  x: X,
  check: Check,
  alert: AlertCircle,
  'alert-triangle': Al<PERSON><PERSON>riangle,
  spinner: Loader2,
  loader: Loader2,
} as const;

interface IconProps {
  name: keyof typeof Icons;
  size?: number;
  className?: string;
}

export const Icon: React.FC<IconProps> = ({ name, size = 20, className = '' }) => {
  const IconComponent = Icons[name];
  return <IconComponent size={size} className={className} />;
};
