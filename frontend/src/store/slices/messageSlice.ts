// frontend/src/store/slices/messageSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Message {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  createdAt: string;
  updatedAt: string;
}

export interface MessageState {
  messages: Record<string, Message[]>; // conversationId -> messages
  loading: boolean;
  error: string | null;
  sendingMessages: Record<string, boolean>; // tempId -> sending status
  optimisticMessageMap: Record<string, string>; // tempId -> realMessageId for correlation
  typingUsers: Record<string, string[]>; // conversationId -> userIds
}

const initialState: MessageState = {
  messages: {},
  loading: false,
  error: null,
  sendingMessages: {},
  optimisticMessageMap: {},
  typingUsers: {}
};

// Async thunks
export const fetchMessages = createAsyncThunk(
  'messages/fetchMessages',
  async ({ conversationId, page = 1 }: { conversationId: string; page?: number }) => {
    const response = await axios.get(`/api/messaging/conversations/${conversationId}/messages/`, {
      params: { page }
    });
    return { conversationId, messages: response.data.results, page };
  }
);

export const sendMessage = createAsyncThunk(
  'messages/sendMessage',
  async ({
    conversationId,
    content,
    messageType = 'TEXT',
    tempId
  }: {
    conversationId: string;
    content: string;
    messageType?: string;
    tempId: string;
  }) => {
    const response = await axios.post(`/api/messaging/conversations/${conversationId}/send/`, {
      content,
      messageType
    });
    return { ...response.data, tempId };
  }
);

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    // Socket event handlers
    addMessage: (state, action: PayloadAction<Message & { tempId?: string }>) => {
      const message = action.payload;
      if (!state.messages[message.conversationId]) {
        state.messages[message.conversationId] = [];
      }

      // Check if this message is replacing an optimistic message
      const conversationMessages = state.messages[message.conversationId];
      let replacedOptimistic = false;

      if (message.tempId) {
        // Find and replace the optimistic message with the same tempId
        const optimisticIndex = conversationMessages.findIndex(msg => msg.id === message.tempId);
        if (optimisticIndex !== -1) {
          conversationMessages[optimisticIndex] = {
            id: message.id,
            conversationId: message.conversationId,
            sender: message.sender,
            content: message.content,
            messageType: message.messageType,
            createdAt: message.createdAt,
            updatedAt: message.updatedAt
          };
          delete state.sendingMessages[message.tempId];
          replacedOptimistic = true;
        }
      }

      // If we didn't replace an optimistic message, check for duplicates by ID
      if (!replacedOptimistic) {
        const existingIndex = conversationMessages.findIndex(msg => msg.id === message.id);
        if (existingIndex === -1) {
          // No duplicate found, add the message
          conversationMessages.push({
            id: message.id,
            conversationId: message.conversationId,
            sender: message.sender,
            content: message.content,
            messageType: message.messageType,
            createdAt: message.createdAt,
            updatedAt: message.updatedAt
          });
        }
      }
    },

    addOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      conversationId: string;
      content: string;
      sender: Message['sender'];
      messageType: string;
    }>) => {
      const { tempId, conversationId, content, sender, messageType } = action.payload;

      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }

      const optimisticMessage: Message = {
        id: tempId,
        conversationId,
        sender,
        content,
        messageType: messageType as Message['messageType'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      state.messages[conversationId].push(optimisticMessage);
      state.sendingMessages[tempId] = true;
    },

    updateOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      message: Message;
    }>) => {
      const { tempId, message } = action.payload;

      // Find and replace the optimistic message
      const conversationMessages = state.messages[message.conversationId];
      if (conversationMessages) {
        const index = conversationMessages.findIndex(msg => msg.id === tempId);
        if (index !== -1) {
          conversationMessages[index] = message;
        }
      }

      delete state.sendingMessages[tempId];
    },

    setTypingUsers: (state, action: PayloadAction<{
      conversationId: string;
      userId: string;
      isTyping: boolean;
    }>) => {
      const { conversationId, userId, isTyping } = action.payload;

      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }

      const typingUsers = state.typingUsers[conversationId];
      const userIndex = typingUsers.indexOf(userId);

      if (isTyping && userIndex === -1) {
        typingUsers.push(userId);
      } else if (!isTyping && userIndex !== -1) {
        typingUsers.splice(userIndex, 1);
      }
    },

    clearMessages: (state, action: PayloadAction<string>) => {
      const conversationId = action.payload;
      delete state.messages[conversationId];
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading = false;
        const { conversationId, messages, page } = action.payload;

        if (page === 1) {
          state.messages[conversationId] = messages;
        } else {
          // Prepend older messages for pagination
          state.messages[conversationId] = [
            ...messages,
            ...(state.messages[conversationId] || [])
          ];
        }
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch messages';
      })
      .addCase(sendMessage.pending, (state, action) => {
        // Optimistic message is already added, just track sending state
        const tempId = action.meta.arg.tempId;
        state.sendingMessages[tempId] = true;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        const { tempId, ...message } = action.payload;

        // Replace optimistic message with real message
        const conversationMessages = state.messages[message.conversationId];
        if (conversationMessages) {
          const index = conversationMessages.findIndex(msg => msg.id === tempId);
          if (index !== -1) {
            conversationMessages[index] = message;
          }
        }

        delete state.sendingMessages[tempId];
      })
      .addCase(sendMessage.rejected, (state, action) => {
        const tempId = action.meta.arg.tempId;
        const conversationId = action.meta.arg.conversationId;

        // Remove failed optimistic message
        const conversationMessages = state.messages[conversationId];
        if (conversationMessages) {
          const index = conversationMessages.findIndex(msg => msg.id === tempId);
          if (index !== -1) {
            conversationMessages.splice(index, 1);
          }
        }

        delete state.sendingMessages[tempId];
        state.error = action.error.message || 'Failed to send message';
      });
  }
});

export const {
  addMessage,
  addOptimisticMessage,
  updateOptimisticMessage,
  setTypingUsers,
  clearMessages,
  setError
} = messageSlice.actions;

export default messageSlice.reducer;
