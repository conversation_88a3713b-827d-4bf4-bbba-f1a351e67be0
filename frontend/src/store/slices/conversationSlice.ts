// frontend/src/store/slices/conversationSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Conversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    sender: {
      username: string;
    };
    createdAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface DraftConversation {
  id: string; // temporary ID
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  }>;
  isDraft: true;
  createdAt: string;
  updatedAt: string;
}

export interface ConversationState {
  conversations: Conversation[];
  draftConversations: DraftConversation[];
  selectedConversationId: string | null;
  loading: boolean;
  error: string | null;
  creating: boolean;
}

const initialState: ConversationState = {
  conversations: [],
  draftConversations: [],
  selectedConversationId: null,
  loading: false,
  error: null,
  creating: false
};

// Async thunks
export const fetchConversations = createAsyncThunk(
  'conversations/fetchConversations',
  async () => {
    const response = await axios.get('/api/messaging/conversations/');
    return response.data.results || response.data;
  }
);

export const createConversation = createAsyncThunk(
  'conversations/createConversation',
  async ({
    type,
    name,
    participantIds
  }: {
    type: 'DIRECT' | 'GROUP';
    name?: string;
    participantIds: string[]
  }) => {
    const response = await axios.post('/api/messaging/conversations/create/', {
      type,
      name,
      participant_ids: participantIds
    });
    return response.data;
  }
);

const conversationSlice = createSlice({
  name: 'conversations',
  initialState,
  reducers: {
    selectConversation: (state, action: PayloadAction<string>) => {
      state.selectedConversationId = action.payload;
    },

    updateConversationLastMessage: (state, action: PayloadAction<{
      conversationId: string;
      message: {
        id: string;
        content: string;
        sender: { username: string };
        createdAt: string;
      };
    }>) => {
      const { conversationId, message } = action.payload;
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.lastMessage = message;
        conversation.updatedAt = message.createdAt;

        // Move conversation to top
        const index = state.conversations.indexOf(conversation);
        state.conversations.splice(index, 1);
        state.conversations.unshift(conversation);
      }
    },

    addConversation: (state, action: PayloadAction<Conversation>) => {
      const newConversation = action.payload;
      const existingIndex = state.conversations.findIndex(c => c.id === newConversation.id);

      if (existingIndex === -1) {
        state.conversations.unshift(newConversation);
      } else {
        state.conversations[existingIndex] = newConversation;
      }
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Draft conversation actions
    createDraftConversation: (state, action: PayloadAction<{
      userId: string;
      username: string;
      first_name: string;
      last_name: string;
      profile_picture?: string;
    }>) => {
      const { userId, username, first_name, last_name, profile_picture } = action.payload;
      const draftId = `draft-${userId}-${Date.now()}`;
      const now = new Date().toISOString();

      const draftConversation: DraftConversation = {
        id: draftId,
        type: 'DIRECT',
        participants: [action.payload],
        isDraft: true,
        createdAt: now,
        updatedAt: now
      };

      // Remove any existing draft with the same user
      state.draftConversations = state.draftConversations.filter(
        draft => !draft.participants.some(p => p.id === userId)
      );

      // Add new draft
      state.draftConversations.unshift(draftConversation);

      // Select the draft conversation
      state.selectedConversationId = draftId;
    },

    removeDraftConversation: (state, action: PayloadAction<string>) => {
      state.draftConversations = state.draftConversations.filter(
        draft => draft.id !== action.payload
      );
    },

    convertDraftToRealConversation: (state, action: PayloadAction<{
      draftId: string;
      realConversation: Conversation;
    }>) => {
      const { draftId, realConversation } = action.payload;

      // Remove the draft
      state.draftConversations = state.draftConversations.filter(
        draft => draft.id !== draftId
      );

      // Add the real conversation
      const existingIndex = state.conversations.findIndex(c => c.id === realConversation.id);
      if (existingIndex === -1) {
        state.conversations.unshift(realConversation);
      } else {
        state.conversations[existingIndex] = realConversation;
      }

      // Update selected conversation ID
      if (state.selectedConversationId === draftId) {
        state.selectedConversationId = realConversation.id;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch conversations';
      })
      .addCase(createConversation.pending, (state) => {
        state.creating = true;
        state.error = null;
      })
      .addCase(createConversation.fulfilled, (state, action) => {
        state.creating = false;
        const newConversation = action.payload;

        // Add to conversations if not already present
        const existingIndex = state.conversations.findIndex(c => c.id === newConversation.id);
        if (existingIndex === -1) {
          state.conversations.unshift(newConversation);
        }

        // Select the new conversation
        state.selectedConversationId = newConversation.id;
      })
      .addCase(createConversation.rejected, (state, action) => {
        state.creating = false;
        state.error = action.error.message || 'Failed to create conversation';
      });
  }
});

export const {
  selectConversation,
  updateConversationLastMessage,
  addConversation,
  setError,
  createDraftConversation,
  removeDraftConversation,
  convertDraftToRealConversation
} = conversationSlice.actions;

export default conversationSlice.reducer;
