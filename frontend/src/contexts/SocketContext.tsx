// frontend/src/contexts/SocketContext.tsx
import React, { createContext, useContext, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from './AuthContext';
import type { AppDispatch } from '../store';
import {
  addMessage,
  addOptimisticMessage,
  setTypingUsers,
  setError as setMessageError
} from '../store/slices/messageSlice';
import {
  updateConversationLastMessage,
  addConversation,
  convertDraftToRealConversation,
  createConversation
} from '../store/slices/conversationSlice';

interface SocketContextType {
  // Connection status
  isConnected: boolean;

  // Message operations
  sendMessage: (conversationId: string, content: string, messageType?: string) => Promise<string>;

  // Conversation operations
  joinConversations: () => void;
  joinConversation: (conversationId: string) => void;

  // Typing indicators
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;

  // User status
  setUserOnline: () => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);
  const { token, isAuthenticated, user } = useAuth();
  const dispatch = useDispatch<AppDispatch>();
  const tempIdToMessageIdRef = useRef<Record<string, string>>({});

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token) {
      console.log('Initializing socket connection...');

      socketRef.current = io('http://localhost:7000', {
        auth: {
          token: token
        }
      });

      setupEventListeners();

      return () => {
        cleanup();
      };
    }
  }, [isAuthenticated, token, dispatch]);

  // Setup all socket event listeners
  const setupEventListeners = useCallback(() => {
    if (!socketRef.current) return;

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('error', handleError);

    // Conversation events
    socket.on('conversations_joined', handleConversationsJoined);
    socket.on('joined_conversation', handleJoinedConversation);

    // Message events
    socket.on('new_message', handleNewMessage);
    socket.on('message_sent', handleMessageSent);

    // Typing events
    socket.on('user_typing', handleUserTyping);

    // User status events
    socket.on('user_status_change', handleUserStatusChange);
  }, [dispatch]);

  // Event handlers
  const handleConnect = useCallback(() => {
    console.log('Connected to socket server');
    setIsConnected(true);

    // Auto-join conversations on connect
    if (socketRef.current) {
      socketRef.current.emit('join_conversations');
      socketRef.current.emit('user_online');
    }
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log('Disconnected from socket server');
    setIsConnected(false);
  }, []);

  const handleError = useCallback((error: { message: string; details?: any }) => {
    console.error('Socket error:', error);
    dispatch(setMessageError(error.message));
  }, [dispatch]);

  const handleConversationsJoined = useCallback((data: {
    success: boolean;
    count: number;
    conversations?: any[]
  }) => {
    console.log(`Joined ${data.count} conversations`);

    if (data.conversations) {
      data.conversations.forEach(conversation => {
        dispatch(addConversation(conversation));
      });
    }
  }, [dispatch]);

  const handleJoinedConversation = useCallback((data: { conversationId: string }) => {
    console.log(`Joined conversation: ${data.conversationId}`);
  }, []);

  const handleNewMessage = useCallback((message: any) => {
    // Check if this message should replace an optimistic message
    const tempId = tempIdToMessageIdRef.current[message.id];

    // Add message to Redux store (with tempId if it exists)
    dispatch(addMessage({
      ...message,
      tempId: tempId
    }));

    // Clean up the mapping
    if (tempId) {
      delete tempIdToMessageIdRef.current[message.id];
    }

    // Update conversation's last message
    dispatch(updateConversationLastMessage({
      conversationId: message.conversationId,
      message: {
        id: message.id,
        content: message.content,
        sender: { username: message.sender.username },
        createdAt: message.createdAt
      }
    }));
  }, [dispatch]);

  const handleMessageSent = useCallback((data: { tempId: string; messageId: string; status: string }) => {
    console.log(`Message sent: ${data.messageId} (temp: ${data.tempId})`);
    // Store the mapping for when the new_message event arrives
    tempIdToMessageIdRef.current[data.messageId] = data.tempId;
  }, []);

  const handleUserTyping = useCallback((data: {
    userId: string;
    conversationId: string;
    isTyping: boolean
  }) => {
    dispatch(setTypingUsers({
      conversationId: data.conversationId,
      userId: data.userId,
      isTyping: data.isTyping
    }));
  }, [dispatch]);

  const handleUserStatusChange = useCallback((data: { userId: string; status: 'online' | 'offline' }) => {
    console.log(`User ${data.userId} is now ${data.status}`);
    // This could update user status in a separate slice if needed
  }, []);

  // Public API methods
  const sendMessage = useCallback(async (conversationId: string, content: string, messageType: string = 'TEXT'): Promise<string> => {
    if (!socketRef.current || !user) {
      console.error('Socket not connected or user not available');
      return '';
    }

    const tempId = uuidv4();
    let actualConversationId = conversationId;

    // Check if this is a draft conversation
    if (conversationId.startsWith('draft-')) {
      try {
        // Extract user ID from draft conversation ID
        const userId = conversationId.split('-')[1];

        // Create the real conversation via API
        const result = await dispatch(createConversation({
          type: 'DIRECT',
          participantIds: [userId]
        })).unwrap();

        actualConversationId = result.id;

        // Convert draft to real conversation in Redux
        dispatch(convertDraftToRealConversation({
          draftId: conversationId,
          realConversation: result
        }));

      } catch (error) {
        console.error('Failed to create conversation:', error);
        return '';
      }
    }

    // Add optimistic message to Redux store
    dispatch(addOptimisticMessage({
      tempId,
      conversationId: actualConversationId,
      content,
      sender: {
        id: user.id,
        username: user.username,
        first_name: user.firstName,
        last_name: user.lastName,
        profile_picture: user.profilePicture
      },
      messageType
    }));

    // Send message through socket
    socketRef.current.emit('send_message', {
      conversationId: actualConversationId,
      content,
      messageType,
      tempId
    });

    return tempId;
  }, [dispatch, user]);

  const joinConversations = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversations');
    }
  }, []);

  const joinConversation = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversation', { conversationId });
    }
  }, []);

  const startTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_start', { conversationId });
    }
  }, []);

  const stopTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_stop', { conversationId });
    }
  }, []);

  const setUserOnline = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('user_online');
    }
  }, []);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (socketRef.current) {
      console.log('Cleaning up socket connection...');

      // Remove all event listeners
      socketRef.current.off('connect');
      socketRef.current.off('disconnect');
      socketRef.current.off('error');
      socketRef.current.off('conversations_joined');
      socketRef.current.off('joined_conversation');
      socketRef.current.off('new_message');
      socketRef.current.off('message_sent');
      socketRef.current.off('user_typing');
      socketRef.current.off('user_status_change');

      // Disconnect socket
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setIsConnected(false);
  }, []);

  const contextValue: SocketContextType = {
    isConnected,
    sendMessage,
    joinConversations,
    joinConversation,
    startTyping,
    stopTyping,
    setUserOnline
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
