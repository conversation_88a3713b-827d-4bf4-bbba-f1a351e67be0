// frontend/src/types/api.ts
import type { 
  ApiResponse, 
  PaginatedResponse, 
  QueryResult, 
  MutationResult,
  CacheTag 
} from './index';

// ============================================================================
// Authentication API Types
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  is_verified: boolean;
  last_seen: string;
  created_at: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthResponse {
  user: AuthUser;
  tokens: AuthTokens;
}

export interface RefreshTokenRequest {
  refresh: string;
}

export interface RefreshTokenResponse {
  access: string;
}

// ============================================================================
// User API Types
// ============================================================================

export interface SearchUser {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  profile_picture?: string;
}

export interface UserProfile extends SearchUser {
  email: string;
  is_verified: boolean;
  last_seen: string;
  created_at: string;
}

export interface UpdateProfileRequest {
  first_name?: string;
  last_name?: string;
  username?: string;
}

// ============================================================================
// Message API Types
// ============================================================================

export interface MessageSender {
  id: string;
  username: string;
  first_name?: string;
  last_name?: string;
  profile_picture?: string;
}

export interface Message {
  id: string;
  conversationId: string;
  sender: MessageSender;
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  createdAt: string;
  updatedAt: string;
  // Optional fields for optimistic updates
  tempId?: string;
  isOptimistic?: boolean;
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
  messageType?: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
}

export interface FetchMessagesRequest {
  conversationId: string;
  page?: number;
  limit?: number;
}

export interface MessagesResponse extends PaginatedResponse<Message> {}

// ============================================================================
// Conversation API Types
// ============================================================================

export interface ConversationParticipant {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
}

export interface ConversationLastMessage {
  id: string;
  content: string;
  sender: {
    username: string;
  };
  createdAt: string;
}

export interface Conversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: ConversationParticipant[];
  lastMessage?: ConversationLastMessage;
  createdAt: string;
  updatedAt: string;
}

export interface CreateConversationRequest {
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participant_ids: string[];
}

export interface ConversationsResponse extends PaginatedResponse<Conversation> {}

// ============================================================================
// RTK Query Hook Types
// ============================================================================

// Auth hooks
export type UseLoginMutation = () => [
  (request: LoginRequest) => Promise<{ data?: ApiResponse<AuthResponse>; error?: any }>,
  MutationResult<ApiResponse<AuthResponse>, LoginRequest>
];

export type UseRegisterMutation = () => [
  (request: RegisterRequest) => Promise<{ data?: ApiResponse<AuthResponse>; error?: any }>,
  MutationResult<ApiResponse<AuthResponse>, RegisterRequest>
];

export type UseLogoutMutation = () => [
  () => Promise<{ data?: void; error?: any }>,
  MutationResult<void, void>
];

export type UseGetCurrentUserQuery = (
  arg: void,
  options?: { skip?: boolean; refetchOnMountOrArgChange?: boolean }
) => QueryResult<ApiResponse<{ user: AuthUser }>>;

// Message hooks
export type UseGetMessagesQuery = (
  request: FetchMessagesRequest,
  options?: { skip?: boolean }
) => QueryResult<MessagesResponse>;

export type UseSendMessageMutation = () => [
  (request: SendMessageRequest) => Promise<{ data?: ApiResponse<Message>; error?: any }>,
  MutationResult<ApiResponse<Message>, SendMessageRequest>
];

// Conversation hooks
export type UseGetConversationsQuery = (
  arg: void,
  options?: { skip?: boolean }
) => QueryResult<ConversationsResponse>;

export type UseCreateConversationMutation = () => [
  (request: CreateConversationRequest) => Promise<{ data?: ApiResponse<Conversation>; error?: any }>,
  MutationResult<ApiResponse<Conversation>, CreateConversationRequest>
];

// User hooks
export type UseSearchUsersQuery = (
  query: string,
  options?: { skip?: boolean }
) => QueryResult<ApiResponse<SearchUser[]>>;

export type UseGetUserProfileQuery = (
  userId: string,
  options?: { skip?: boolean }
) => QueryResult<ApiResponse<UserProfile>>;

// ============================================================================
// Cache Management Types
// ============================================================================

export interface CacheUpdateOptions {
  optimistic?: boolean;
  invalidate?: CacheTag[];
  merge?: boolean;
}

export interface OptimisticUpdate<T> {
  id: string;
  data: T;
  revert: () => void;
}

// ============================================================================
// Error Types
// ============================================================================

export interface RTKQueryError {
  status: number;
  data: {
    success: false;
    error: string;
    details?: Array<{
      field: string;
      message: string;
    }>;
    timestamp: string;
  };
}

export interface NetworkError {
  status: 0;
  error: string;
}

export type ApiError = RTKQueryError | NetworkError;
