// socket-server/src/events/socketEvents.ts
import { Server, Socket } from 'socket.io';
import { MessageService } from '../services/messageService';
import { ConversationService } from '../services/conversationService';
import { z } from 'zod';
import {
  MessageCreateSchema,
  JoinRoomSchema,
  TypingEventSchema
} from '../schemas';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export class SocketEventHandler {
  private io: Server;
  private messageService: MessageService;
  private conversationService: ConversationService;

  constructor(
    io: Server,
    messageService: MessageService,
    conversationService: ConversationService
  ) {
    this.io = io;
    this.messageService = messageService;
    this.conversationService = conversationService;
  }

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to messaging`);

    // Join user to their conversations
    socket.on('join_conversations', () => this.handleJoinConversations(socket));

    // Handle new message creation
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));

    // Handle typing indicators
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));

    // Handle joining specific conversation room
    socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));

    // Handle user status
    socket.on('user_online', () => this.handleUserOnline(socket));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  private async handleJoinConversations(socket: AuthenticatedSocket) {
    try {
      const conversations = await this.conversationService.getUserConversations(socket.userId);

      // Join all conversation rooms
      for (const conversation of conversations) {
        socket.join(`conversation_${conversation.id}`);
      }

      socket.emit('conversations_joined', {
        success: true,
        count: conversations.length,
        conversations: conversations
      });
    } catch (error) {
      console.error('Error joining conversations:', error);
      socket.emit('error', { message: 'Failed to join conversations' });
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      const message = await this.messageService.createMessage(data, socket.userId);

      // Emit to all participants in the conversation
      this.io.to(`conversation_${message.conversationId}`).emit('new_message', {
        id: message.id,
        conversationId: message.conversationId,
        sender: message.sender,
        content: message.content,
        messageType: message.messageType,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt
      });

      // Acknowledge to sender
      socket.emit('message_sent', {
        tempId: data.tempId, // For optimistic UI updates
        messageId: message.id,
        status: 'delivered'
      });

    } catch (error) {
      console.error('Error sending message:', error);
      if (error instanceof z.ZodError) {
        socket.emit('error', {
          message: 'Invalid message data',
          details: error.issues
        });
      } else {
        socket.emit('error', {
          message: error instanceof Error ? error.message : 'Failed to send message'
        });
      }
    }
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: true
        });
      }
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: false
        });
      }
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  private async handleJoinConversation(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      const hasAccess = await this.conversationService.joinConversation(socket.userId, conversationId);
      if (hasAccess) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Access denied to conversation' });
      }
    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  private async handleUserOnline(socket: AuthenticatedSocket) {
    try {
      await this.messageService.updateUserStatus(socket.userId, true);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  private async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} disconnected from messaging`);

    try {
      await this.messageService.updateUserStatus(socket.userId, false);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    } catch (error) {
      console.error('Error updating user offline status:', error);
    }
  }
}
