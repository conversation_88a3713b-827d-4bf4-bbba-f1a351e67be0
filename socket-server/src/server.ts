// socket-server/src/server.ts
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { authenticateSocket, AuthenticatedSocket } from './middleware/auth';
import { MessageService } from './services/messageService';
import { ConversationService } from './services/conversationService';
import { SocketEventHandler } from './events/socketEvents';

const app = express();
const server = createServer(app);
const prisma = new PrismaClient();

// Initialize services
const messageService = new MessageService(prisma);
const conversationService = new ConversationService(prisma);

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:6000', 'http://127.0.0.1:6000'],
  credentials: true,
}));

// Socket.io server setup
const io = new Server(server, {
  cors: {
    origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:6000', 'http://127.0.0.1:6000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Initialize socket event handler
const socketEventHandler = new SocketEventHandler(io, messageService, conversationService);

// Authentication middleware
io.use(authenticateSocket);

// Socket connection handler
io.on('connection', (socket) => {
  const authSocket = socket as AuthenticatedSocket;
  console.log(`User ${authSocket.user.username} connected`);

  // Join user to their personal room
  authSocket.join(`user:${authSocket.userId}`);

  // Initialize message handlers using the service layer
  socketEventHandler.handleConnection(authSocket);
});

const PORT = process.env.PORT || 7000;

server.listen(PORT, () => {
  console.log(`Socket server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  server.close();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  server.close();
});
