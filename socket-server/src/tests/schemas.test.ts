// socket-server/src/tests/schemas.test.ts
import {
  UserSchema,
  MessageSchema,
  CreateMessageSchema,
  ConversationSchema,
  CreateConversationSchema,
  JoinRoomSchema,
  LeaveRoomSchema,
  TypingSchema,
  AuthTokenSchema,
} from '../schemas';

describe('Zod Schema Validation Tests', () => {
  describe('UserSchema', () => {
    it('should validate a valid user object', () => {
      const validUser = {
        id: '123e4567-e89b-12d3-a456-************',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        profilePicture: 'https://example.com/avatar.jpg',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(validUser);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidUser = {
        id: '123e4567-e89b-12d3-a456-************',
        email: 'invalid-email',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
    });

    it('should reject invalid UUID', () => {
      const invalidUser = {
        id: 'invalid-uuid',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isVerified: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = UserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
    });
  });

  describe('CreateMessageSchema', () => {
    it('should validate a valid message creation request', () => {
      const validMessage = {
        content: 'Hello, world!',
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = CreateMessageSchema.safeParse(validMessage);
      expect(result.success).toBe(true);
    });

    it('should reject empty content', () => {
      const invalidMessage = {
        content: '',
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = CreateMessageSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });

    it('should reject content that is too long', () => {
      const invalidMessage = {
        content: 'a'.repeat(1001), // Exceeds 1000 character limit
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = CreateMessageSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });

    it('should reject invalid conversation ID', () => {
      const invalidMessage = {
        content: 'Hello, world!',
        conversationId: 'invalid-uuid',
      };

      const result = CreateMessageSchema.safeParse(invalidMessage);
      expect(result.success).toBe(false);
    });
  });

  describe('CreateConversationSchema', () => {
    it('should validate a valid conversation creation request', () => {
      const validConversation = {
        name: 'Test Conversation',
        isGroup: true,
        participantIds: [
          '123e4567-e89b-12d3-a456-************',
          '123e4567-e89b-12d3-a456-************',
        ],
      };

      const result = CreateConversationSchema.safeParse(validConversation);
      expect(result.success).toBe(true);
    });

    it('should validate conversation without name (direct message)', () => {
      const validConversation = {
        isGroup: false,
        participantIds: ['123e4567-e89b-12d3-a456-************'],
      };

      const result = CreateConversationSchema.safeParse(validConversation);
      expect(result.success).toBe(true);
    });

    it('should reject empty participant list', () => {
      const invalidConversation = {
        name: 'Test Conversation',
        isGroup: true,
        participantIds: [],
      };

      const result = CreateConversationSchema.safeParse(invalidConversation);
      expect(result.success).toBe(false);
    });

    it('should reject invalid participant UUIDs', () => {
      const invalidConversation = {
        name: 'Test Conversation',
        isGroup: true,
        participantIds: ['invalid-uuid'],
      };

      const result = CreateConversationSchema.safeParse(invalidConversation);
      expect(result.success).toBe(false);
    });
  });

  describe('JoinRoomSchema', () => {
    it('should validate a valid join room request', () => {
      const validJoinRoom = {
        conversationId: '123e4567-e89b-12d3-a456-************',
      };

      const result = JoinRoomSchema.safeParse(validJoinRoom);
      expect(result.success).toBe(true);
    });

    it('should reject invalid conversation ID', () => {
      const invalidJoinRoom = {
        conversationId: 'invalid-uuid',
      };

      const result = JoinRoomSchema.safeParse(invalidJoinRoom);
      expect(result.success).toBe(false);
    });
  });

  describe('TypingSchema', () => {
    it('should validate a valid typing indicator', () => {
      const validTyping = {
        conversationId: '123e4567-e89b-12d3-a456-************',
        isTyping: true,
      };

      const result = TypingSchema.safeParse(validTyping);
      expect(result.success).toBe(true);
    });

    it('should reject invalid conversation ID', () => {
      const invalidTyping = {
        conversationId: 'invalid-uuid',
        isTyping: true,
      };

      const result = TypingSchema.safeParse(invalidTyping);
      expect(result.success).toBe(false);
    });

    it('should reject non-boolean isTyping', () => {
      const invalidTyping = {
        conversationId: '123e4567-e89b-12d3-a456-************',
        isTyping: 'true', // Should be boolean, not string
      };

      const result = TypingSchema.safeParse(invalidTyping);
      expect(result.success).toBe(false);
    });
  });

  describe('AuthTokenSchema', () => {
    it('should validate a valid auth token', () => {
      const validToken = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      };

      const result = AuthTokenSchema.safeParse(validToken);
      expect(result.success).toBe(true);
    });

    it('should reject empty token', () => {
      const invalidToken = {
        token: '',
      };

      const result = AuthTokenSchema.safeParse(invalidToken);
      expect(result.success).toBe(false);
    });
  });
});
