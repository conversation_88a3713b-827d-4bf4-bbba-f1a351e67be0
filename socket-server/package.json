{"name": "socket-server", "version": "1.0.0", "description": "Socket.io server for chat application", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio"}, "keywords": ["socket.io", "chat", "websocket"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.12.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "redis": "^5.6.1", "socket.io": "^4.8.1", "zod": "^4.0.5"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "prisma": "^6.12.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}